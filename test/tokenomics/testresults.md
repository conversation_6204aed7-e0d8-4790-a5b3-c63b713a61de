npm run test:tokenomics

> WarpSector-game@1.0.0 test:tokenomics
> node test/tokenomics/runStressTest.js

🚀 Starting WarpSector Tokenomics Stress Test
============================================================
📋 Test Configuration:
   Duration: 300 seconds
   Max Users: 10
   API URL: http://localhost:3001/api
   Hardhat URL: http://localhost:8545
   Hot Wallet: ******************************************
   Scenario: All scenarios
   Verbose: Disabled

🔍 Validating prerequisites...
   ✅ Game Server: OK
   ✅ Hardhat Node: OK
   ✅ Hot Wallet Balance: OK
   ✅ Test Accounts: OK
✅ All prerequisites validated

🧪 TokenomicsStressTest initialized
📊 Configuration: {
  apiBaseUrl: 'http://localhost:3001/api',
  hardhatUrl: 'http://localhost:8545',
  chainId: 31337,
  hotWalletAddress: '******************************************',
  testDuration: 300000,
  maxConcurrentUsers: 10,
  verbose: false,
  reportFile: null,
  scenario: null
}
🚀 Initializing Tokenomics Stress Test Framework...
✅ Server health check passed: { status: 'OK', message: 'AI Service Server is running' }
🔗 Connected to network: Network {}
💰 Hot wallet balance: 100.0 ETH
👥 Found 20 test accounts
📊 TransactionTracker initialized
💰 TreasuryMonitor initialized
📊 Monitoring systems initialized
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created grinder user simulator: grinder_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created grinder user simulator: grinder_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 grinder_3: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created grinder user simulator: grinder_3 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 whale_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created whale user simulator: whale_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 whale_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created whale user simulator: whale_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 creator_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created creator user simulator: creator_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 creator_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created creator user simulator: creator_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_1: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created casual user simulator: casual_1 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_2: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created casual user simulator: casual_2 (******************************************)
✅ Initialized daily reward tracker for user: ******************************************
🧪 casual_3: ETH Test Mode ENABLED - using ETH pricing (10% of WISH prices)
👤 Created casual user simulator: casual_3 (******************************************)
👥 Created 10 user simulators
✅ Stress test framework initialized successfully
🚀 Starting Tokenomics Stress Test...
💰 Initial treasury balance: 100 ETH
🚀 TreasuryMonitor started
🚀 TransactionTracker started
📊 Monitoring started
🔍 Queue health monitoring started
🎮 Running Sequential Grinding Test (Attack Vector 1)...
📋 Objective: Test if grinder earnings can drain treasury
👥 Testing with 3 grinder accounts
🎨 Running Creator Reward Test (Attack Vector 2)...
📋 Objective: Test creator reward sustainability and 50% distribution accuracy
👨‍🎨 Testing with 2 creators and 2 whales
🎨 Phase 1: Environment Creation
🎨 creator_1 creating environment: "Desert oasis with sandstorms"
💸 creator_1 spending 2500 ETH for: Reality Warp: Desert oasis with sandstorms
💸 creator_1 sending 2500 ETH to hot wallet for: Reality Warp: Desert oasis with sandstorms
👥 Running Multi-Account Coordination Test (Attack Vector 3)...
📋 Objective: Test economic balance under coordinated behavior
🤝 Simulating coordinated behavior across 5 accounts
⚡ Starting coordinated actions...
💰 Running Treasury Drain Test (Maximum Stress)...
📋 Objective: Test maximum stress on treasury with all users acting simultaneously
💰 Initial treasury balance: 100 ETH
🎯 Starting grinder session: grinder_1
🎮 Level 1: 1110 WISH → ETH (let game calculate)
🎁 Awarding 111 ETH to grinder_1 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1110 WISH → 111 ETH)
💰 Pre-stress treasury balance: 100 ETH
⚡ Initiating maximum stress scenario...
🔢 Next nonce for creator_1: 0
⚡ grinder_1 starting maximum stress behavior
🎮 Level 1: 1059 WISH → ETH (let game calculate)
🎁 Awarding 105.9 ETH to grinder_1 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1059 WISH → 105.9 ETH)
✅ REAL blockchain transaction mined: 0xbc052ae0148bb27663cf3fde061a428600c8a2ef08cf2f3cdf9dfe321f7ffecf
⛽ Gas used: 21000
💸 creator_1 completed REAL ETH transfer: 2500 ETH to hot wallet
🤝 grinder_1 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_1...
🔐 Authenticated grinder_1 with server
🎮 Level 1: 1133 WISH → ETH (let game calculate)
🎁 Awarding 113.30000000000001 ETH to grinder_1 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1133 WISH → 113.30000000000001 ETH)
✅ API Call: POST /wallet/send (234ms)
✅ REAL ETH transfer completed: 111 ETH to ******************************************
📤 Transaction hash: 0x7615e396ffab086bd3cabbb140ecf690f220369c52fcc40b453f1a6b59f3cb8a
⚡ grinder_2 starting maximum stress behavior
🎮 Level 1: 1032 WISH → ETH (let game calculate)
🎁 Awarding 103.2 ETH to grinder_2 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1032 WISH → 103.2 ETH)
⚡ grinder_3 starting maximum stress behavior
🎮 Level 1: 1216 WISH → ETH (let game calculate)
🎁 Awarding 121.60000000000001 ETH to grinder_3 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1216 WISH → 121.60000000000001 ETH)
✅ API Call: POST /generate-environment (157ms)
🎨 Environment Creation Tracked: Desert oasis with Environment by creator_1
✅ Environment created by creator_1: Desert oasis with Environment
✅ Environment created: Desert oasis with Environment by creator_1
✅ Environment created: Desert oasis with Environment by creator_1
🎨 creator_2 creating environment: "Ice planet with aurora borealis"
💸 creator_2 spending 2500 ETH for: Reality Warp: Ice planet with aurora borealis
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Ice planet with aurora borealis
🔢 Next nonce for creator_2: 0
🤝 grinder_2 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_2...
🔐 Authenticated grinder_2 with server
🎮 Level 1: 1117 WISH → ETH (let game calculate)
🎁 Awarding 111.7 ETH to grinder_2 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1117 WISH → 111.7 ETH)
✅ API Call: POST /wallet/send (175ms)
✅ REAL ETH transfer completed: 103.2 ETH to ******************************************
📤 Transaction hash: 0x6d384dff4d869a803d863fb2e3dbed8958944f6ae004a7ef3b757ca89f50af0e
⚡ whale_1 starting maximum stress behavior
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_1 sending 1500 ETH to hot wallet for: Extra Life
✅ REAL blockchain transaction mined: 0x9f9a8649bac0e5dd04fa311852491cb95bc22335c1a034034d2475db6928465c
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
🔢 Next nonce for whale_1: 0
⚡ whale_2 starting maximum stress behavior
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_2 sending 1500 ETH to hot wallet for: Extra Life
🤝 grinder_3 starting coordinated behavior simulation
🎮 Starting grinder session for grinder_3...
🔐 Authenticated grinder_3 with server
🎮 Level 1: 1214 WISH → ETH (let game calculate)
🎁 Awarding 121.4 ETH to grinder_3 for: Level 1 completion - 100% enemies defeated (ETH test mode: 1214 WISH → 121.4 ETH)
✅ REAL blockchain transaction mined: 0x7a4c66cb8e56f1715969c500dcd28f5d6a4ee09cc60cd8db5bd62caf44322190
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 373.41278491426954ms before next transaction...
🔢 Next nonce for whale_2: 0
✅ API Call: POST /wallet/send (302ms)
✅ REAL ETH transfer completed: 121.60000000000001 ETH to ******************************************
📤 Transaction hash: 0xa451398c39adb1eb95dfd4d22e5f3de4be2d081ac3b6755a47f934f2b89d2e77
✅ REAL blockchain transaction mined: 0xbaadd156d08bc3e6cd7363b245f3c8df2eef005f01004488162ecc2c1d646080
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 352.53504576343187ms before next transaction...
⚡ creator_1 starting maximum stress behavior
🎨 creator_1 creating environment: "Desert oasis with sandstorms"
💸 creator_1 spending 2500 ETH for: Reality Warp: Desert oasis with sandstorms
💸 creator_1 sending 2500 ETH to hot wallet for: Reality Warp: Desert oasis with sandstorms
✅ API Call: POST /generate-environment (120ms)
🎨 Environment Creation Tracked: Ice planet with Environment by creator_2
✅ Environment created by creator_2: Ice planet with Environment
✅ Environment created: Ice planet with Environment by creator_2
✅ Environment created: Ice planet with Environment by creator_2
💰 Phase 2: Mystical Environment Purchases
🐋 whale_1 purchasing Mystical Environment for 25000 tokens
🎁 Expected creator reward: 12500 tokens
💰 whale_1 purchasing Mystical Environment for 1000 ETH
🎁 Expected creator reward: 500 ETH
🔢 Next nonce for creator_1: 1
🤝 whale_1 starting coordinated behavior simulation
🎮 Starting whale session for whale_1...
🔐 Authenticated whale_1 with server
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_1 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_1: 1
⚡ creator_2 starting maximum stress behavior
🎨 creator_2 creating environment: "Underwater coral reef with bioluminescence"
💸 creator_2 spending 2500 ETH for: Reality Warp: Underwater coral reef with bioluminescence
💸 creator_2 sending 2500 ETH to hot wallet for: Reality Warp: Underwater coral reef with bioluminescence
🎮 Level 2: 1124 WISH → ETH (let game calculate)
🎁 Awarding 112.4 ETH to grinder_1 for: Level 2 completion - 100% enemies defeated (ETH test mode: 1124 WISH → 112.4 ETH)
🔢 Next nonce for creator_2: 1
✅ REAL blockchain transaction mined: 0x7806b830c14b82586788c1a6dba9c010f30f011ae6a7e626fcf9939270397fef
⛽ Gas used: 21000
💸 creator_1 completed REAL ETH transfer: 2500 ETH to hot wallet
✅ REAL blockchain transaction mined: 0xf743b895ad6d64acf381c008910ac674f991a3acdc44c64872920353134e7071
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 315.880464854116ms before next transaction...
⚡ casual_1 starting maximum stress behavior
✅ REAL blockchain transaction mined: 0xe2e68df4c95a35f2ec83dc98fb0a1078e5b2a4d02967b8397982fe6fff42ea08
⛽ Gas used: 21000
💸 creator_2 completed REAL ETH transfer: 2500 ETH to hot wallet
🎮 Level 2: 1116 WISH → ETH (let game calculate)
🎁 Awarding 111.60000000000001 ETH to grinder_2 for: Level 2 completion - 100% enemies defeated (ETH test mode: 1116 WISH → 111.60000000000001 ETH)
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_1 sending 1000 ETH to hot wallet for: Extra Wingman
🤝 whale_2 starting coordinated behavior simulation
🎮 Starting whale session for whale_2...
🔐 Authenticated whale_2 with server
🛒 Extra Life: 1500 ETH (ETH test mode)
💸 whale_2 sending 1500 ETH to hot wallet for: Extra Life
🔢 Next nonce for whale_1: 2
⚡ casual_2 starting maximum stress behavior
🔢 Next nonce for whale_2: 1
✅ API Call: POST /wallet/send (841ms)
✅ REAL ETH transfer completed: 105.9 ETH to ******************************************
📤 Transaction hash: 0x6248b3673f66aa217535a55c73a04232ca28448e757a38a49777b1f9116073e7
✅ API Call: POST /wallet/send (812ms)
✅ REAL ETH transfer completed: 113.30000000000001 ETH to ******************************************
📤 Transaction hash: 0x3b1b8329ebd377e9a3374e0319042a74c7827127970c0085df9e08c08d892f19
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_2 sending 1000 ETH to hot wallet for: Extra Wingman
✅ API Call: POST /generate-environment (136ms)
🎨 Environment Creation Tracked: Desert oasis with Environment by creator_1
✅ Environment created by creator_1: Desert oasis with Environment
✅ Environment created: Desert oasis with Environment by creator_1
🔢 Next nonce for whale_2: 2
✅ API Call: POST /generate-environment (126ms)
🎨 Environment Creation Tracked: Underwater coral reef Environment by creator_2
✅ Environment created by creator_2: Underwater coral reef Environment
✅ Environment created: Underwater coral reef Environment by creator_2
✅ REAL blockchain transaction mined: 0x6c7e292cf2a8897565820af72c26a19b015123247ec89ee1c51633bcf4487e41
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1000 ETH to hot wallet
✅ REAL blockchain transaction mined: 0x8c02f12cae037990a9b1f8400f625df1b39e08650362dbec604f86673b437abe
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1500 ETH to hot wallet
⏳ Waiting 523.415574428536ms before next transaction...
⏳ Waiting 335.89471197956794ms before next transaction...
⚡ casual_3 starting maximum stress behavior
🎮 Level 2: 1161 WISH → ETH (let game calculate)
🎁 Awarding 116.10000000000001 ETH to grinder_3 for: Level 2 completion - 100% enemies defeated (ETH test mode: 1161 WISH → 116.10000000000001 ETH)
✅ REAL blockchain transaction mined: 0x80ac08aeca5357f7ea7d0f8d1daa34d1ed1f0b00362bcf7c771c8ca141e41c3d
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 580.5472317893849ms before next transaction...
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_1 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_1: 3
✅ API Call: POST /wallet/send (811ms)
✅ REAL ETH transfer completed: 111.7 ETH to ******************************************
📤 Transaction hash: 0x6d96a499ed96e503a5a99835c8ae5292287eef39e6b563f7bf7bc64d9fccf5c1
✅ REAL Mystical Environment purchased: Test Environment for 1000 ETH
🎁 Creator reward distributed: 500 ETH to user_1756654556315_5s6n73ksu
✅ REAL blockchain transaction mined: 0xb858ff72093f336796193e7a98e88dfaaa2516d16f7888a54d6ceb65ac75d355
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 570.8689476579523ms before next transaction...
✅ API Call: POST /wallet/send (783ms)
✅ REAL ETH transfer completed: 121.4 ETH to ******************************************
📤 Transaction hash: 0xc0cfe97b88171bfa77a0183aa92e63023f15285e197e84a95a3d0ee3ecd5961a
🛒 Extra Wingman: 1000 ETH (ETH test mode)
💸 whale_2 sending 1000 ETH to hot wallet for: Extra Wingman
🔢 Next nonce for whale_2: 3
🚫 Level 2: No reward (level_already_completed_today)
🚫 Level 2: No reward (level_already_completed_today)
✅ REAL blockchain transaction mined: 0x257c6c856e3a8ed3730cf6ebdf139fd840affff69589ee1d527fec15a60eb1dc
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 1000 ETH to hot wallet
⏳ Waiting 503.58072001635185ms before next transaction...
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_1 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_1: 4
🚫 Level 2: No reward (level_already_completed_today)
✅ REAL blockchain transaction mined: 0x2bac24bf52794d09c87cbf7fb8e39dd294cd30463d905c43ab6ba9a24f6e0fd8
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 775.3399717342842ms before next transaction...
✅ API Call: POST /wallet/send (939ms)
✅ REAL ETH transfer completed: 112.4 ETH to ******************************************
📤 Transaction hash: 0x781177a47bdf73e0fff9a9de25cdc533ca0cde891f364897e3e200d9978a627c
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_2 sending 750 ETH to hot wallet for: Spread Ammo
🐋 whale_2 purchasing Mystical Environment for 25000 tokens
🎁 Expected creator reward: 12500 tokens
💰 whale_2 purchasing Mystical Environment for 1000 ETH
🎁 Expected creator reward: 500 ETH
🔢 Next nonce for whale_2: 4
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_1 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_1: 5
✅ REAL blockchain transaction mined: 0x84e2c5aeea24dcd00b9e1b6721904cfe8cdd2f0f7a84d47ff8969255e55b6a0a
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 731.9499413504465ms before next transaction...
🚫 Level 2: No reward (level_already_completed_today)
✅ REAL blockchain transaction mined: 0x85e44f1783d06a78cc84ea1fb5540d685ff4327bc460d6813eb6a08e0cd6ae2b
⛽ Gas used: 21000
💸 whale_1 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 749.4217962590038ms before next transaction...
✅ REAL Mystical Environment purchased: Test Environment for 1000 ETH
🎁 Creator reward distributed: 500 ETH to user_1756654556315_5s6n73ksu
✅ API Call: POST /wallet/send (997ms)
✅ REAL ETH transfer completed: 111.60000000000001 ETH to ******************************************
📤 Transaction hash: 0xf270f855cadf8374f5c0a2f07ad8f5346a5c4f3eeb1b84a50a998c9e32e8e442
🎮 Level 3: 1087 WISH → ETH (let game calculate)
🎁 Awarding 108.7 ETH to grinder_1 for: Level 3 completion - 100% enemies defeated (ETH test mode: 1087 WISH → 108.7 ETH)
🚫 Level 3: No reward (level_already_completed_today)
🛒 Spread Ammo: 750 ETH (ETH test mode)
💸 whale_2 sending 750 ETH to hot wallet for: Spread Ammo
🔢 Next nonce for whale_2: 5
✅ API Call: POST /wallet/send (992ms)
✅ REAL ETH transfer completed: 116.10000000000001 ETH to ******************************************
📤 Transaction hash: 0x90c52c0a0b6dacc9793c02f2474a10176e7347a039e0ab323a6f81cf83c4aae7
✅ REAL blockchain transaction mined: 0x7dc6b096240e3f7eab240ed5dce916890c7b48af20e7fcfbc00567006fa927e0
⛽ Gas used: 21000
💸 whale_2 completed REAL ETH transfer: 750 ETH to hot wallet
⏳ Waiting 780.7357746524123ms before next transaction...
🎮 Level 3: 1078 WISH → ETH (let game calculate)
🎁 Awarding 107.80000000000001 ETH to grinder_2 for: Level 3 completion - 100% enemies defeated (ETH test mode: 1078 WISH → 107.80000000000001 ETH)
🚫 Level 3: No reward (level_already_completed_today)
🎮 Level 3: 1153 WISH → ETH (let game calculate)
🎁 Awarding 115.30000000000001 ETH to grinder_3 for: Level 3 completion - 100% enemies defeated (ETH test mode: 1153 WISH → 115.30000000000001 ETH)
📊 Creator Reward Test Results:
   Total Purchases: 50000 tokens
   Expected Creator Rewards: 25000 tokens (50%)
   Environments Created: 2
   Creator Reward Accuracy: Testing 50% distribution...
🚫 Level 3: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (444ms)
✅ REAL ETH transfer completed: 108.7 ETH to ******************************************
📤 Transaction hash: 0x8d84634587c08d25f63df99545d8242539e2c4ad01467ef2f572866047d2c318
🎮 Level 4: 1111 WISH → ETH (let game calculate)
🎁 Awarding 111.10000000000001 ETH to grinder_1 for: Level 4 completion - 100% enemies defeated (ETH test mode: 1111 WISH → 111.10000000000001 ETH)
🚫 Level 3: No reward (level_already_completed_today)
🎁 Awarding 37.5 ETH to whale_1 for: Level 1 completion - minimal effort (ETH test mode: 375 WISH → 37.5 ETH)
✅ API Call: POST /wallet/send (479ms)
✅ REAL ETH transfer completed: 107.80000000000001 ETH to ******************************************
📤 Transaction hash: 0xf6478981bcb67fb7c05fb24cf44ab34178f645099d785720fc0282ad225a7a06
🚫 Level 4: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (240ms)
✅ REAL ETH transfer completed: 37.5 ETH to ******************************************
📤 Transaction hash: 0x631abbd28b5f66fed23d938c92393a9b4f95950154e4080194f673d321a1a3ad
✅ API Call: POST /wallet/send (556ms)
✅ REAL ETH transfer completed: 115.30000000000001 ETH to ******************************************
📤 Transaction hash: 0x3688221104f3f305542e87e673587eba2004fcb2b3d0b10ae43b9800fb96a145
🎁 Awarding 37.5 ETH to whale_2 for: Level 1 completion - minimal effort (ETH test mode: 375 WISH → 37.5 ETH)
🎮 Level 4: 1500 WISH → ETH (let game calculate)
🎁 Awarding 150 ETH to grinder_2 for: Level 4 completion - 100% enemies defeated (ETH test mode: 1500 WISH → 150 ETH)
🚫 Level 4: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (159ms)
✅ REAL ETH transfer completed: 37.5 ETH to ******************************************
📤 Transaction hash: 0xe9c4770152e728d1afb0ac00b40e334a1aa83d52b02a805cecd1ca94a170b9b4
🎮 Level 4: 1162 WISH → ETH (let game calculate)
🎁 Awarding 116.2 ETH to grinder_3 for: Level 4 completion - 100% enemies defeated (ETH test mode: 1162 WISH → 116.2 ETH)
🚫 Level 4: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (688ms)
✅ REAL ETH transfer completed: 111.10000000000001 ETH to ******************************************
📤 Transaction hash: 0x5440c494b9718fa59a272ad25bbf4527361f411c045fb2cd2cc40cf54f648c5c
🎮 Level 5: 1138 WISH → ETH (let game calculate)
🎁 Awarding 113.80000000000001 ETH to grinder_1 for: Level 5 completion - 100% enemies defeated (ETH test mode: 1138 WISH → 113.80000000000001 ETH)
🚫 Level 4: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (469ms)
✅ REAL ETH transfer completed: 150 ETH to ******************************************
📤 Transaction hash: 0xef3a76caa9fca9adb86d3f1602968ecb5744722f597efca8a62bd998de04cb1a
🚫 Level 5: No reward (level_already_completed_today)
✅ API Call: POST /wallet/send (477ms)
✅ REAL ETH transfer completed: 116.2 ETH to ******************************************
📤 Transaction hash: 0xde5e04742371caf0661da7709ca41b433cc7697f7d621d465e5645c9ce503fb8
🎮 Level 5: 1141 WISH → ETH (let game calculate)
🎁 Awarding 114.10000000000001 ETH to grinder_2 for: Level 5 completion - 100% enemies defeated (ETH test mode: 1141 WISH → 114.10000000000001 ETH)
🚫 Level 5: No reward (level_already_completed_today)
🎁 Awarding 75 ETH to whale_1 for: Level 2 completion - minimal effort (ETH test mode: 750 WISH → 75 ETH)
🎮 Level 5: 1034 WISH → ETH (let game calculate)
🎁 Awarding 103.4 ETH to grinder_3 for: Level 5 completion - 100% enemies defeated (ETH test mode: 1034 WISH → 103.4 ETH)
🚫 Level 5: No reward (level_already_completed_today)
🎮 Level 6: 2400 WISH → ETH (let game calculate)
🎁 Awarding 240 ETH to grinder_1 for: Level 6 completion - 100% enemies defeated (ETH test mode: 2400 WISH → 240 ETH)
✅ API Call: POST /wallet/send (762ms)
✅ REAL ETH transfer completed: 113.80000000000001 ETH to ******************************************
📤 Transaction hash: 0x8ba6d7bf7210db1009de94a88625d0a5a41a211cfbaee4827dfbdb2d9d6a000e
⚠️ grinder_2: Rate limited (429) for http://localhost:3001/api/wallet/send, retrying in 57000ms
✅ API Call: POST /wallet/send (177ms)
✅ REAL ETH transfer completed: 75 ETH to ******************************************
📤 Transaction hash: 0x9c7e749e6c8f93f89e31716d7faac7d3c6516453a657a690bd9f111ba897d6f0
🎁 Awarding 75 ETH to whale_2 for: Level 2 completion - minimal effort (ETH test mode: 750 WISH → 75 ETH)
🚫 Level 5: No reward (level_already_completed_today)
🚫 Level 6: No reward (level_already_completed_today)
⚠️ grinder_3: Rate limited (429) for http://localhost:3001/api/wallet/send, retrying in 56000ms
🎮 Level 6: 2400 WISH → ETH (let game calculate)
🎁 Awarding 240 ETH to grinder_2 for: Level 6 completion - 100% enemies defeated (ETH test mode: 2400 WISH → 240 ETH)
🚫 Level 6: No reward (level_already_completed_today)
⚠️ grinder_1: Rate limited (429) for http://localhost:3001/api/wallet/send, retrying in 56000ms
🎮 Level 6: 1830 WISH → ETH (let game calculate)
🎁 Awarding 183 ETH to grinder_3 for: Level 6 completion - 100% enemies defeated (ETH test mode: 1830 WISH → 183 ETH)
🎮 Level 7: 2400 WISH → ETH (let game calculate)
🎁 Awarding 240 ETH to grinder_1 for: Level 7 completion - 100% enemies defeated (ETH test mode: 2400 WISH → 240 ETH)
🚫 Level 7: No reward (level_already_completed_today)
💰 Balance change: +19898.899477 ETH (19998.899477 ETH total)
📈 Treasury inflow detected: +19898.899477 ETH
⚠️ ALERT: Extremely large balance change detected: 19898.899477 ETH
💰 Whale whale_1 completed session with heavy spending
✅ Completed whale session for whale_1
🎮 Level 8: 1721 WISH → ETH (let game calculate)
🎁 Awarding 172.10000000000002 ETH to grinder_1 for: Level 8 completion - 100% enemies defeated (ETH test mode: 1721 WISH → 172.10000000000002 ETH)
❌ ETH reward failed for whale_2: Error: Queue acquisition timed out after 45000ms
    at Timeout._onTimeout (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:68:36)
    at listOnTimeout (node:internal/timers:573:17)
    at process.processTimers (node:internal/timers:514:7)
❌ Error in whale session for whale_2: Error: Queue acquisition timed out after 45000ms
    at Timeout._onTimeout (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:68:36)
    at listOnTimeout (node:internal/timers:573:17)
    at process.processTimers (node:internal/timers:514:7)
❌ Stress test failed: Error: Queue acquisition timed out after 45000ms
    at Timeout._onTimeout (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:68:36)
    at listOnTimeout (node:internal/timers:573:17)
    at process.processTimers (node:internal/timers:514:7)
🔍 Queue health monitoring stopped
🧹 Cleaning up rate limiter resources...
📊 Final rate limiter status: {}
❌ ETH reward failed for grinder_2: Error: Test completed - queued requests cancelled
    at file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:241:33
    at Array.forEach (<anonymous>)
    at Object.cleanup (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:240:35)
    at TokenomicsStressTest.cleanupRateLimiter (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:1067:32)
    at TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:667:28)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)
❌ ETH reward failed for grinder_3: Error: Test completed - queued requests cancelled
    at file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:241:33
    at Array.forEach (<anonymous>)
    at Object.cleanup (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:240:35)
    at TokenomicsStressTest.cleanupRateLimiter (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:1067:32)
    at TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:667:28)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)
❌ ETH reward failed for grinder_1: Error: Test completed - queued requests cancelled
    at file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:241:33
    at Array.forEach (<anonymous>)
    at Object.cleanup (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:240:35)
    at TokenomicsStressTest.cleanupRateLimiter (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:1067:32)
    at TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:667:28)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)
❌ ETH reward failed for grinder_1: Error: Test completed - queued requests cancelled
    at file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:241:33
    at Array.forEach (<anonymous>)
    at Object.cleanup (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:240:35)
    at TokenomicsStressTest.cleanupRateLimiter (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:1067:32)
    at TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:667:28)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)
❌ Error in grinder session for grinder_1: Error: Test completed - queued requests cancelled
    at file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:241:33
    at Array.forEach (<anonymous>)
    at Object.cleanup (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:240:35)
    at TokenomicsStressTest.cleanupRateLimiter (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:1067:32)
    at TokenomicsStressTest.runStressTest (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:667:28)
    at async StressTestRunner.run (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/runStressTest.js:186:26)
✅ Rate limiter cleanup completed
❌ Stress test failed: Error: Queue acquisition timed out after 45000ms
    at Timeout._onTimeout (file:///home/<USER>/Downloads/AIGames/OrangeDefense/test/tokenomics/TokenomicsStressTest.js:68:36)
    at listOnTimeout (node:internal/timers:573:17)
    at process.processTimers (node:internal/timers:514:7)



npm run network

> WarpSector-game@1.0.0 network
> node start.js network


> WarpSector-game@1.0.0 server:dev
> cd server && npm run dev


> WarpSector-server@1.0.0 dev
> nodemon index.js


  VITE v5.4.20  ready in 364 ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: http://*************:3000/
  ➜  press h + enter to show help
[nodemon] 3.1.10
[nodemon] to restart at any time, enter `rs`
[nodemon] watching path(s): *.*
[nodemon] watching extensions: js,mjs,cjs,json
[nodemon] starting `node index.js`
🔐 Hot wallet initialized securely on server
Server running on port 3001
[Middleware] Concurrent limiter middleware called for GET /api/health
[ConcurrentLimiter] Acquiring slot for request 1, currently 0/3 active
[ConcurrentLimiter] Processing request 1 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 1
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for GET /api/environments
[ConcurrentLimiter] Acquiring slot for request 2, currently 0/3 active
[ConcurrentLimiter] Processing request 2 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 2
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for GET /api/health
[ConcurrentLimiter] Acquiring slot for request 3, currently 0/3 active
[ConcurrentLimiter] Processing request 3 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 3
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for GET /api/environments
[ConcurrentLimiter] Acquiring slot for request 4, currently 0/3 active
[ConcurrentLimiter] Processing request 4 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 4
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for GET /api/health
[ConcurrentLimiter] Acquiring slot for request 5, currently 0/3 active
[ConcurrentLimiter] Processing request 5 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 5
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for GET /api/wallet/balance
[ConcurrentLimiter] Acquiring slot for request 6, currently 0/3 active
[ConcurrentLimiter] Processing request 6 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 6
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for GET /api/health
[ConcurrentLimiter] Acquiring slot for request 7, currently 0/3 active
[ConcurrentLimiter] Processing request 7 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 7
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 8, currently 0/3 active
[ConcurrentLimiter] Processing request 8 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 8
💰 Sending 111 ETH from hot wallet to ****************************************** for ETH reward: Level 1 completion - 100% enemies defeated
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
💰 Hot wallet balance before transaction: 100.0 ETH
🔢 Next nonce for hot wallet: 0 (network: 0, pending: 1)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
📤 Transaction sent: 0x7615e396ffab086bd3cabbb140ecf690f220369c52fcc40b453f1a6b59f3cb8a
📤 Transaction sent: 0x7615e396ffab086bd3cabbb140ecf690f220369c52fcc40b453f1a6b59f3cb8a
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
✅ Transaction confirmed in block 2
💰 Hot wallet balance after transaction: 100.0 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 9, currently 0/3 active
[ConcurrentLimiter] Processing request 9 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 9
💰 Sending 103.2 ETH from hot wallet to ****************************************** for ETH reward: Level 1 completion - 100% enemies defeated
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
💰 Hot wallet balance before transaction: 100.0 ETH
🔢 Next nonce for hot wallet: 1 (network: 0, pending: 2)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
📤 Transaction sent: 0x6d384dff4d869a803d863fb2e3dbed8958944f6ae004a7ef3b757ca89f50af0e
📤 Transaction sent: 0x6d384dff4d869a803d863fb2e3dbed8958944f6ae004a7ef3b757ca89f50af0e
✅ Transaction confirmed in block 3
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
💰 Hot wallet balance after transaction: 2385.799927844672084 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 10, currently 0/3 active
[ConcurrentLimiter] Processing request 10 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 10
💰 Sending 121.60000000000001 ETH from hot wallet to ****************************************** for ETH reward: Level 1 completion - 100% enemies defeated
💰 Hot wallet balance before transaction: 2385.799927844672084 ETH
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
🔄 Resetting nonce tracking: network=2, pending=2
🔢 Next nonce for hot wallet: 2 (network: 2, pending: 3)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
📤 Transaction sent: 0xa451398c39adb1eb95dfd4d22e5f3de4be2d081ac3b6755a47f934f2b89d2e77
📤 Transaction sent: 0xa451398c39adb1eb95dfd4d22e5f3de4be2d081ac3b6755a47f934f2b89d2e77
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
✅ Transaction confirmed in block 6
💰 Hot wallet balance after transaction: 2385.799927844672084 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 11, currently 0/3 active
[ConcurrentLimiter] Processing request 11 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 11
💰 Sending 105.9 ETH from hot wallet to ****************************************** for ETH reward: Level 1 completion - 100% enemies defeated
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 12, currently 1/3 active
[ConcurrentLimiter] Processing request 12 immediately, 2/3 active
[ConcurrentLimiter] Resolving request 12
💰 Sending 113.30000000000001 ETH from hot wallet to ****************************************** for ETH reward: Level 1 completion - 100% enemies defeated
💰 Hot wallet balance before transaction: 11764.199897410541879 ETH
💰 Hot wallet balance before transaction: 11764.199897410541879 ETH
🔄 Resetting nonce tracking: network=3, pending=3
🔢 Next nonce for hot wallet: 3 (network: 3, pending: 4)
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
🔢 Next nonce for hot wallet: 4 (network: 3, pending: 5)
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
📤 Transaction sent: 0x6248b3673f66aa217535a55c73a04232ca28448e757a38a49777b1f9116073e7
📤 Transaction sent: 0x3b1b8329ebd377e9a3374e0319042a74c7827127970c0085df9e08c08d892f19
📤 Transaction sent: 0x6248b3673f66aa217535a55c73a04232ca28448e757a38a49777b1f9116073e7
📤 Transaction sent: 0x3b1b8329ebd377e9a3374e0319042a74c7827127970c0085df9e08c08d892f19
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
✅ Transaction confirmed in block 11
✅ Transaction confirmed in block 12
💰 Hot wallet balance after transaction: 11764.199897410541879 ETH
💰 Hot wallet balance after transaction: 11764.199897410541879 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 2/3 active
[ResponseHooks] Slot released, now 1/3 active
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/environments/env_1_1756654556342/purchase
[ConcurrentLimiter] Acquiring slot for request 13, currently 0/3 active
[ConcurrentLimiter] Processing request 13 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 13
🛒 [whale_1-1758907634297] Environment purchase request: whale_1 buying env_1_1756654556342 for 1000 ETH (fallback: undefined, retry: undefined)
🧪 [whale_1-1758907634297] Stress test mode: Skipping environments file save to prevent server restart
🎁 Distributing creator reward: 500 ETH to user_1756654556315_5s6n73ksu (50% of 1000)
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 14, currently 1/3 active
[ConcurrentLimiter] Processing request 14 immediately, 2/3 active
[ConcurrentLimiter] Resolving request 14
💰 Sending 111.7 ETH from hot wallet to ****************************************** for ETH reward: Level 1 completion - 100% enemies defeated
💰 Hot wallet balance before transaction: 11764.199897410541879 ETH
⚠️ Invalid creator address user_1756654556315_5s6n73ksu, sending to burn address instead
💰 Sending 500 ETH from hot wallet to ****************************************** for Creator reward for environment purchase: Custom Environment (burned - invalid creator address: user_1756654556315_5s6n73ksu)
💰 Hot wallet balance before transaction: 11764.199897410541879 ETH
🔢 Next nonce for hot wallet: 5 (network: 3, pending: 6)
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
🔢 Next nonce for hot wallet: 6 (network: 5, pending: 7)
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
📤 Transaction sent: 0x6d96a499ed96e503a5a99835c8ae5292287eef39e6b563f7bf7bc64d9fccf5c1
📤 Transaction sent: 0x6d96a499ed96e503a5a99835c8ae5292287eef39e6b563f7bf7bc64d9fccf5c1
✅ Transaction confirmed in block 16
📤 Transaction sent: 0xc903b6cf7876d464ca82deaa24848b8e8db724091370aea7ea2cf41e91070e17
📤 Transaction sent: 0xc903b6cf7876d464ca82deaa24848b8e8db724091370aea7ea2cf41e91070e17
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
✅ Transaction confirmed in block 17
💰 Hot wallet balance after transaction: 16933.299799664481359 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 2/3 active
[ResponseHooks] Slot released, now 1/3 active
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 15, currently 0/3 active
[ConcurrentLimiter] Processing request 15 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 15
💰 Sending 121.4 ETH from hot wallet to ****************************************** for ETH reward: Level 1 completion - 100% enemies defeated
💰 Hot wallet balance after transaction: 16933.299799664481359 ETH
💰 Hot wallet balance before transaction: 16933.299799664481359 ETH
✅ Creator reward distributed successfully: dist_1758907634817_q4b7n6kyn
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
🔢 Next nonce for hot wallet: 7 (network: 5, pending: 8)
📤 Transaction sent: 0xc0cfe97b88171bfa77a0183aa92e63023f15285e197e84a95a3d0ee3ecd5961a
📤 Transaction sent: 0xc0cfe97b88171bfa77a0183aa92e63023f15285e197e84a95a3d0ee3ecd5961a
✅ Transaction confirmed in block 19
💰 Hot wallet balance after transaction: 16933.299799664481359 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 16, currently 0/3 active
[ConcurrentLimiter] Processing request 16 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 16
💰 Sending 112.4 ETH from hot wallet to ****************************************** for ETH reward: Level 2 completion - 100% enemies defeated
💰 Hot wallet balance before transaction: 18811.899776997481241 ETH
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
🔄 Resetting nonce tracking: network=8, pending=8
🔢 Next nonce for hot wallet: 8 (network: 8, pending: 9)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
📤 Transaction sent: 0x781177a47bdf73e0fff9a9de25cdc533ca0cde891f364897e3e200d9978a627c
📤 Transaction sent: 0x781177a47bdf73e0fff9a9de25cdc533ca0cde891f364897e3e200d9978a627c
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
✅ Transaction confirmed in block 21
💰 Hot wallet balance after transaction: 18811.899776997481241 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/environments/env_1_1756654556342/purchase
[ConcurrentLimiter] Acquiring slot for request 17, currently 0/3 active
[ConcurrentLimiter] Processing request 17 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 17
🛒 [whale_2-1758907635331] Environment purchase request: whale_2 buying env_1_1756654556342 for 1000 ETH (fallback: undefined, retry: undefined)
🧪 [whale_2-1758907635331] Stress test mode: Skipping environments file save to prevent server restart
🎁 Distributing creator reward: 500 ETH to user_1756654556315_5s6n73ksu (50% of 1000)
⚠️ Invalid creator address user_1756654556315_5s6n73ksu, sending to burn address instead
💰 Sending 500 ETH from hot wallet to ****************************************** for Creator reward for environment purchase: Custom Environment (burned - invalid creator address: user_1756654556315_5s6n73ksu)
💰 Hot wallet balance before transaction: 18811.899776997481241 ETH
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 18, currently 1/3 active
[ConcurrentLimiter] Processing request 18 immediately, 2/3 active
[ConcurrentLimiter] Resolving request 18
💰 Sending 111.60000000000001 ETH from hot wallet to ****************************************** for ETH reward: Level 2 completion - 100% enemies defeated
🔢 Next nonce for hot wallet: 9 (network: 8, pending: 10)
💰 Hot wallet balance before transaction: 18811.899776997481241 ETH
🔢 Next nonce for hot wallet: 10 (network: 8, pending: 11)
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
📤 Transaction sent: 0x87289468f407d172d9b45a5702d1c296b31f2089e83b261bf48b8e0c27222d02
📤 Transaction sent: 0x87289468f407d172d9b45a5702d1c296b31f2089e83b261bf48b8e0c27222d02
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
✅ Transaction confirmed in block 24
📤 Transaction sent: 0xf270f855cadf8374f5c0a2f07ad8f5346a5c4f3eeb1b84a50a998c9e32e8e442
📤 Transaction sent: 0xf270f855cadf8374f5c0a2f07ad8f5346a5c4f3eeb1b84a50a998c9e32e8e442
✅ Transaction confirmed in block 26
💰 Hot wallet balance after transaction: 20337.899711209257849 ETH
✅ Creator reward distributed successfully: dist_1758907635528_69dd8amhc
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 2/3 active
[ResponseHooks] Slot released, now 1/3 active
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
💰 Hot wallet balance after transaction: 20337.899711209257849 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 19, currently 0/3 active
[ConcurrentLimiter] Processing request 19 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 19
💰 Sending 116.10000000000001 ETH from hot wallet to ****************************************** for ETH reward: Level 2 completion - 100% enemies defeated
💰 Hot wallet balance before transaction: 20337.899711209257849 ETH
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
🔄 Resetting nonce tracking: network=11, pending=11
🔢 Next nonce for hot wallet: 11 (network: 11, pending: 12)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
📤 Transaction sent: 0x90c52c0a0b6dacc9793c02f2474a10176e7347a039e0ab323a6f81cf83c4aae7
📤 Transaction sent: 0x90c52c0a0b6dacc9793c02f2474a10176e7347a039e0ab323a6f81cf83c4aae7
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
✅ Transaction confirmed in block 27
💰 Hot wallet balance after transaction: 20337.899711209257849 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 20, currently 0/3 active
[ConcurrentLimiter] Processing request 20 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 20
💰 Sending 108.7 ETH from hot wallet to ****************************************** for ETH reward: Level 3 completion - 100% enemies defeated
💰 Hot wallet balance before transaction: 20971.79968963554458 ETH
🔄 Resetting nonce tracking: network=12, pending=12
🔢 Next nonce for hot wallet: 12 (network: 12, pending: 13)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
📤 Transaction sent: 0x8d84634587c08d25f63df99545d8242539e2c4ad01467ef2f572866047d2c318
📤 Transaction sent: 0x8d84634587c08d25f63df99545d8242539e2c4ad01467ef2f572866047d2c318
✅ Transaction confirmed in block 29
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
💰 Hot wallet balance after transaction: 20971.79968963554458 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 21, currently 0/3 active
[ConcurrentLimiter] Processing request 21 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 21
💰 Sending 107.80000000000001 ETH from hot wallet to ****************************************** for ETH reward: Level 3 completion - 100% enemies defeated
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
💰 Hot wallet balance before transaction: 20971.79968963554458 ETH
🔢 Next nonce for hot wallet: 13 (network: 12, pending: 14)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
📤 Transaction sent: 0xf6478981bcb67fb7c05fb24cf44ab34178f645099d785720fc0282ad225a7a06
📤 Transaction sent: 0xf6478981bcb67fb7c05fb24cf44ab34178f645099d785720fc0282ad225a7a06
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
✅ Transaction confirmed in block 30
💰 Hot wallet balance after transaction: 20755.299646811545856 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 22, currently 0/3 active
[ConcurrentLimiter] Processing request 22 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 22
💰 Sending 37.5 ETH from hot wallet to ****************************************** for ETH reward: Level 1 completion - minimal effort
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 23, currently 1/3 active
[ConcurrentLimiter] Processing request 23 immediately, 2/3 active
[ConcurrentLimiter] Resolving request 23
💰 Sending 115.30000000000001 ETH from hot wallet to ****************************************** for ETH reward: Level 3 completion - 100% enemies defeated
💰 Hot wallet balance before transaction: 20755.299646811545856 ETH
💰 Hot wallet balance before transaction: 20755.299646811545856 ETH
🔄 Resetting nonce tracking: network=14, pending=14
🔢 Next nonce for hot wallet: 14 (network: 14, pending: 15)
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
🔢 Next nonce for hot wallet: 15 (network: 14, pending: 16)
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
📤 Transaction sent: 0x631abbd28b5f66fed23d938c92393a9b4f95950154e4080194f673d321a1a3ad
📤 Transaction sent: 0x3688221104f3f305542e87e673587eba2004fcb2b3d0b10ae43b9800fb96a145
📤 Transaction sent: 0x631abbd28b5f66fed23d938c92393a9b4f95950154e4080194f673d321a1a3ad
📤 Transaction sent: 0x3688221104f3f305542e87e673587eba2004fcb2b3d0b10ae43b9800fb96a145
✅ Transaction confirmed in block 31
✅ Transaction confirmed in block 32
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
💰 Hot wallet balance after transaction: 20755.299646811545856 ETH
💰 Hot wallet balance after transaction: 20755.299646811545856 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 2/3 active
[ResponseHooks] Slot released, now 1/3 active
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 24, currently 0/3 active
[ConcurrentLimiter] Processing request 24 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 24
💰 Sending 37.5 ETH from hot wallet to ****************************************** for ETH reward: Level 1 completion - minimal effort
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
💰 Hot wallet balance before transaction: 20602.499604180419406 ETH
🔢 Next nonce for hot wallet: 16 (network: 14, pending: 17)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
📤 Transaction sent: 0xe9c4770152e728d1afb0ac00b40e334a1aa83d52b02a805cecd1ca94a170b9b4
📤 Transaction sent: 0xe9c4770152e728d1afb0ac00b40e334a1aa83d52b02a805cecd1ca94a170b9b4
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 25, currently 1/3 active
[ConcurrentLimiter] Processing request 25 immediately, 2/3 active
[ConcurrentLimiter] Resolving request 25
💰 Sending 111.10000000000001 ETH from hot wallet to ****************************************** for ETH reward: Level 4 completion - 100% enemies defeated
✅ Transaction confirmed in block 33
💰 Hot wallet balance before transaction: 20602.499604180419406 ETH
💰 Hot wallet balance after transaction: 20602.499604180419406 ETH
🔄 Resetting nonce tracking: network=17, pending=17
🔢 Next nonce for hot wallet: 17 (network: 17, pending: 18)
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 2/3 active
[ResponseHooks] Slot released, now 1/3 active
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
📤 Transaction sent: 0x5440c494b9718fa59a272ad25bbf4527361f411c045fb2cd2cc40cf54f648c5c
📤 Transaction sent: 0x5440c494b9718fa59a272ad25bbf4527361f411c045fb2cd2cc40cf54f648c5c
✅ Transaction confirmed in block 34
💰 Hot wallet balance after transaction: 20453.899561697019892 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 26, currently 0/3 active
[ConcurrentLimiter] Processing request 26 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 26
💰 Sending 150 ETH from hot wallet to ****************************************** for ETH reward: Level 4 completion - 100% enemies defeated
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
💰 Hot wallet balance before transaction: 20453.899561697019892 ETH
🔢 Next nonce for hot wallet: 18 (network: 17, pending: 19)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
📤 Transaction sent: 0xef3a76caa9fca9adb86d3f1602968ecb5744722f597efca8a62bd998de04cb1a
📤 Transaction sent: 0xef3a76caa9fca9adb86d3f1602968ecb5744722f597efca8a62bd998de04cb1a
✅ Transaction confirmed in block 35
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
💰 Hot wallet balance after transaction: 20453.899561697019892 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 27, currently 0/3 active
[ConcurrentLimiter] Processing request 27 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 27
💰 Sending 116.2 ETH from hot wallet to ****************************************** for ETH reward: Level 4 completion - 100% enemies defeated
💰 Hot wallet balance before transaction: 20303.899540499571214 ETH
🔄 Resetting nonce tracking: network=19, pending=19
🔢 Next nonce for hot wallet: 19 (network: 19, pending: 20)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
📤 Transaction sent: 0xde5e04742371caf0661da7709ca41b433cc7697f7d621d465e5645c9ce503fb8
📤 Transaction sent: 0xde5e04742371caf0661da7709ca41b433cc7697f7d621d465e5645c9ce503fb8
✅ Transaction confirmed in block 36
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
💰 Hot wallet balance after transaction: 20303.899540499571214 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 28, currently 0/3 active
[ConcurrentLimiter] Processing request 28 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 28
💰 Sending 113.80000000000001 ETH from hot wallet to ****************************************** for ETH reward: Level 5 completion - 100% enemies defeated
💰 Hot wallet balance before transaction: 20187.69951932676906 ETH
🔄 Resetting nonce tracking: network=20, pending=20
🔢 Next nonce for hot wallet: 20 (network: 20, pending: 21)
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 29, currently 1/3 active
[ConcurrentLimiter] Processing request 29 immediately, 2/3 active
[ConcurrentLimiter] Resolving request 29
💰 Sending 75 ETH from hot wallet to ****************************************** for ETH reward: Level 2 completion - minimal effort
📤 Transaction sent: 0x8ba6d7bf7210db1009de94a88625d0a5a41a211cfbaee4827dfbdb2d9d6a000e
📤 Transaction sent: 0x8ba6d7bf7210db1009de94a88625d0a5a41a211cfbaee4827dfbdb2d9d6a000e
💰 Hot wallet balance before transaction: 20187.69951932676906 ETH
[QueueProcessor] Processing queue: 0 queued, 2/3 active, 1 available slots
✅ Transaction confirmed in block 37
🔢 Next nonce for hot wallet: 21 (network: 20, pending: 22)
💰 Hot wallet balance after transaction: 20187.69951932676906 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 2/3 active
[ResponseHooks] Slot released, now 1/3 active
[QueueProcessor] Processing queue: 0 queued, 1/3 active, 2 available slots
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
📤 Transaction sent: 0x9c7e749e6c8f93f89e31716d7faac7d3c6516453a657a690bd9f111ba897d6f0
📤 Transaction sent: 0x9c7e749e6c8f93f89e31716d7faac7d3c6516453a657a690bd9f111ba897d6f0
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 30, currently 0/3 active
[ConcurrentLimiter] Processing request 30 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 30
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
✅ Transaction confirmed in block 38
💰 Hot wallet balance after transaction: 19998.89947704318232 ETH
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 31, currently 0/3 active
[ConcurrentLimiter] Processing request 31 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 31
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[Middleware] Concurrent limiter middleware called for POST /api/wallet/send
[ConcurrentLimiter] Acquiring slot for request 32, currently 0/3 active
[ConcurrentLimiter] Processing request 32 immediately, 1/3 active
[ConcurrentLimiter] Resolving request 32
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 1/3 active
[ResponseHooks] Slot released, now 0/3 active
[ResponseHooks] res.json called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.send called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release
[ResponseHooks] res.end called
[ResponseHooks] Releasing slot, currently 0/3 active
[ResponseHooks] No active requests to release


eth_sendRawTransaction
  Transaction: 0x8ba6d7bf7210db1009de94a88625d0a5a41a211cfbaee4827dfbdb2d9d6a000e
  From:        ******************************************
  To:          ******************************************
  Value:       113.8 ETH
  Gas used:    21000 of 21001
  Block #37:   0xaaac48d3b0a91eb5b3fd1fd3b41d9d73ac2019e9a4572b28a613c6420a721f68

eth_getTransactionReceipt
eth_chainId (2)
eth_estimateGas
eth_chainId (2)
eth_sendRawTransaction
  Transaction: 0x9c7e749e6c8f93f89e31716d7faac7d3c6516453a657a690bd9f111ba897d6f0
  From:        ******************************************
  To:          ******************************************
  Value:       75. ETH
  Gas used:    21000 of 21001
  Block #38:   0x43e0725aa674bdd350a7fd5228b5982235e7305128089a07e19997ab4d54026f

eth_chainId (2)
eth_getTransactionReceipt
eth_getBalance
eth_chainId
eth_getBalance
eth_chainId
eth_getBalance
eth_chainId
eth_getBalance
eth_chainId
eth_getBalance
eth_chainId
eth_getBalance
eth_chainId
eth_getBalance
eth_chainId
eth_getBalance
eth_chainId
eth_getBalance
eth_chainId
eth_getBalance
eth_chainId
eth_getBalance
eth_chainId
