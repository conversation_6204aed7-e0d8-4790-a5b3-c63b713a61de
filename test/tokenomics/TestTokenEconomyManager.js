import { TokenEconomyManager } from '../../src/managers/TokenEconomyManager.js';
import { DailyRewardTracker } from '../../src/managers/DailyRewardTracker.js';

/**
 * Test-compatible version of TokenEconomyManager that works in Node.js environment
 * Simulates the browser environment needed for testing
 */
export class TestTokenEconomyManager extends TokenEconomyManager {
    constructor(testConfig = {}) {
        super();
        
        // Test configuration
        this.testConfig = {
            ethTestMode: true,
            walletAddress: testConfig.walletAddress || '******************************************',
            walletBalance: testConfig.walletBalance || 100000, // 100k ETH for testing
            ...testConfig
        };
        
        // Set up test environment
        this.setupTestEnvironment();
    }
    
    /**
     * Set up test environment to simulate browser globals
     */
    setupTestEnvironment() {
        // Simulate wallet connection
        this.walletConnected = true;
        this.walletAddress = this.testConfig.walletAddress;
        this.walletBalance = this.testConfig.walletBalance;
        this.walletProvider = 'test';
        
        // Initialize daily reward tracker
        this.dailyRewardTracker = new DailyRewardTracker(this.walletAddress);
        
        // Mock window.gameEngine for ETH test mode checks
        this.mockGameEngine = {
            ethTestModeManager: {
                isTestMode: this.testConfig.ethTestMode
            }
        };
        
        console.log(`✅ Test TokenEconomyManager initialized:`, {
            walletAddress: this.walletAddress,
            walletBalance: this.walletBalance,
            ethTestMode: this.testConfig.ethTestMode
        });
    }
    
    /**
     * Override canAfford to use test environment
     */
    canAfford(amount) {
        if (this.testConfig.ethTestMode) {
            return this.walletBalance >= amount;
        }
        return this.playerBalance >= amount;
    }
    
    /**
     * Override getBalance to use test environment
     */
    getBalance() {
        if (this.testConfig.ethTestMode) {
            return this.walletBalance;
        }
        return this.playerBalance;
    }
    
    /**
     * Override awardTokens to work in test environment
     */
    async awardTokens(amount, reason, metadata = {}) {
        // Add transaction to history
        const transaction = {
            id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'earn',
            amount: amount,
            reason: reason,
            timestamp: Date.now(),
            metadata: metadata
        };
        
        this.transactionHistory.unshift(transaction);
        if (this.transactionHistory.length > this.maxHistorySize) {
            this.transactionHistory.pop();
        }
        
        // Update balances
        this.playerBalance += amount;
        this.totalEarned += amount;
        
        // For level completion, update daily tracking
        if (reason === 'level_completion' && metadata.levelNumber) {
            const levelNumber = metadata.levelNumber;
            const completionPercentage = metadata.completionPercentage || 1.0;
            
            // Record in daily tracker
            this.dailyRewardTracker.recordLevelCompletion(levelNumber, completionPercentage, amount);
        }
        
        // Check if we're in ETH test mode (use mock instead of window)
        const isEthTestMode = this.testConfig.ethTestMode;
        
        if (isEthTestMode) {
            // In ETH test mode, convert WISH to ETH and send from hot wallet
            const ethAmount = amount * 0.1; // 10% conversion rate
            
            // Simulate sending ETH from hot wallet (this would be done by the server)
            console.log(`💰 ETH Test Mode: Would send ${ethAmount} ETH from hot wallet for ${amount} WISH tokens`);
            
            // For testing, we simulate the hot wallet transaction
            const sessionId = metadata.sessionId || 'test_session';
            return await this.sendFromHotWallet(ethAmount, sessionId);
        }
        
        // Trigger callbacks
        this.triggerBalanceUpdate();
        this.triggerTransaction(transaction);
        
        return {
            success: true,
            amount: amount,
            newBalance: this.playerBalance,
            transaction: transaction
        };
    }
    
    /**
     * Mock sendFromHotWallet for testing
     */
    async sendFromHotWallet(ethAmount, sessionId) {
        console.log(`🔥 Mock hot wallet: Sending ${ethAmount} ETH (session: ${sessionId})`);
        
        // Simulate successful transaction
        return {
            success: true,
            transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`,
            amount: ethAmount,
            sessionId: sessionId
        };
    }
    
    /**
     * Override spendTokens to work in test environment
     */
    async spendTokens(amount, reason, metadata = {}) {
        // Check if we're in ETH test mode
        if (this.testConfig.ethTestMode) {
            // In ETH test mode, spend from wallet balance
            if (this.walletBalance < amount) {
                return {
                    success: false,
                    reason: 'insufficient_balance',
                    required: amount,
                    available: this.walletBalance
                };
            }
            
            // Deduct from wallet balance
            this.walletBalance -= amount;
            
            // Add transaction to history
            const transaction = {
                id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                type: 'spend',
                amount: amount,
                reason: reason,
                timestamp: Date.now(),
                metadata: metadata
            };
            
            this.transactionHistory.unshift(transaction);
            this.totalSpent += amount;
            
            return {
                success: true,
                amount: amount,
                newBalance: this.walletBalance,
                transaction: transaction
            };
        } else {
            // Use parent implementation for WISH tokens
            return super.spendTokens(amount, reason, metadata);
        }
    }
    
    /**
     * Override wallet connection check
     */
    isWalletConnected() {
        return this.walletConnected;
    }
    
    /**
     * Get test statistics
     */
    getTestStatistics() {
        return {
            ...this.getStatistics(),
            testConfig: this.testConfig,
            mockGameEngine: this.mockGameEngine
        };
    }
}
