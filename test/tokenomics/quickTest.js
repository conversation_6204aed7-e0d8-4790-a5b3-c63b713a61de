#!/usr/bin/env node

/**
 * Quick Test for Tokenomics Stress Testing Framework
 * 
 * This script performs a quick validation of the stress testing framework
 * to ensure all components are working correctly before running full tests.
 */

import { TokenomicsStressTest } from './TokenomicsStressTest.js';
import { UserSimulator } from './UserSimulator.js';
import { TransactionTracker } from './TransactionTracker.js';
import { TreasuryMonitor } from './TreasuryMonitor.js';
import { ValidationSystem } from './ValidationSystem.js';
import fetch from 'node-fetch';

class QuickTest {
    constructor() {
        this.config = {
            apiBaseUrl: 'http://localhost:3001/api',
            hardhatUrl: 'http://localhost:8545',
            chainId: 31337,
            hotWalletAddress: '******************************************',
            testDuration: 30000, // 30 seconds for quick test
            maxConcurrentUsers: 3
        };
    }

    async run() {
        console.log('🧪 Running Quick Test for Tokenomics Stress Testing Framework');
        console.log('=' .repeat(70));

        try {
            await this.testPrerequisites();
            await this.testComponentInitialization();
            await this.testBasicFunctionality();
            await this.testUserSimulation();
            
            console.log('');
            console.log('✅ All quick tests passed! Framework is ready for stress testing.');
            console.log('');
            console.log('Next steps:');
            console.log('1. Run full stress test: node test/tokenomics/runStressTest.js');
            console.log('2. Or run specific scenario: node test/tokenomics/runStressTest.js --scenario grinder');
            
        } catch (error) {
            console.error('❌ Quick test failed:', error.message);
            console.log('');
            console.log('Please fix the issues above before running stress tests.');
            process.exit(1);
        }
    }

    async testPrerequisites() {
        console.log('🔍 Testing Prerequisites...');

        // Test 1: Game Server
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/health`);
            if (!response.ok) throw new Error(`Server returned ${response.status}`);
            console.log('   ✅ Game Server: Running');
        } catch (error) {
            throw new Error(`Game Server not available: ${error.message}`);
        }

        // Test 2: Hardhat Node
        try {
            const response = await fetch(this.config.hardhatUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'eth_chainId',
                    params: [],
                    id: 1
                })
            });
            const result = await response.json();
            const chainId = parseInt(result.result, 16);
            if (chainId !== this.config.chainId) {
                throw new Error(`Wrong chain ID: expected ${this.config.chainId}, got ${chainId}`);
            }
            console.log('   ✅ Hardhat Node: Running (Chain ID 31337)');
        } catch (error) {
            throw new Error(`Hardhat Node not available: ${error.message}`);
        }

        // Test 3: Hot Wallet
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/wallet/balance`, {
                headers: { 'Authorization': 'Bearer test-token' }
            });
            if (!response.ok) throw new Error(`Balance check failed: ${response.status}`);
            const result = await response.json();
            const balance = parseFloat(result.balance);
            if (balance < 0.1) {
                throw new Error(`Hot wallet balance too low: ${balance} ETH`);
            }
            console.log(`   ✅ Hot Wallet: ${balance} ETH available`);
        } catch (error) {
            throw new Error(`Hot Wallet check failed: ${error.message}`);
        }

        console.log('');
    }

    async testComponentInitialization() {
        console.log('🔧 Testing Component Initialization...');

        // Test TokenomicsStressTest initialization
        try {
            const stressTest = new TokenomicsStressTest(this.config);
            console.log('   ✅ TokenomicsStressTest: Initialized');
        } catch (error) {
            throw new Error(`TokenomicsStressTest initialization failed: ${error.message}`);
        }

        // Test UserSimulator initialization
        try {
            const mockAccount = {
                address: '******************************************',
                balance: '10000',
                balanceWei: '10000000000000000000000'
            };
            const userSim = new UserSimulator({
                type: 'grinder',
                account: mockAccount,
                config: this.config,
                id: 'test_grinder'
            });
            console.log('   ✅ UserSimulator: Initialized');
        } catch (error) {
            throw new Error(`UserSimulator initialization failed: ${error.message}`);
        }

        // Test TransactionTracker initialization
        try {
            const tracker = new TransactionTracker(this.config);
            console.log('   ✅ TransactionTracker: Initialized');
        } catch (error) {
            throw new Error(`TransactionTracker initialization failed: ${error.message}`);
        }

        // Test TreasuryMonitor initialization
        try {
            const { ethers } = await import('ethers');
            const provider = new ethers.JsonRpcProvider(this.config.hardhatUrl);
            const monitor = new TreasuryMonitor(this.config, provider);
            console.log('   ✅ TreasuryMonitor: Initialized');
        } catch (error) {
            throw new Error(`TreasuryMonitor initialization failed: ${error.message}`);
        }

        // Test ValidationSystem initialization
        try {
            const validator = new ValidationSystem(this.config);
            console.log('   ✅ ValidationSystem: Initialized');
        } catch (error) {
            throw new Error(`ValidationSystem initialization failed: ${error.message}`);
        }

        console.log('');
    }

    async testBasicFunctionality() {
        console.log('⚙️ Testing Basic Functionality...');

        // Test API endpoints
        try {
            // Test token award endpoint
            const awardResponse = await fetch(`${this.config.apiBaseUrl}/tokens/award`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer test-token'
                },
                body: JSON.stringify({
                    userId: 'test_user',
                    amount: 100,
                    reason: 'Quick test'
                })
            });

            if (!awardResponse.ok) {
                throw new Error(`Token award failed: ${awardResponse.status}`);
            }

            console.log('   ✅ Token Award API: Working');

            // Test token spend endpoint
            const spendResponse = await fetch(`${this.config.apiBaseUrl}/tokens/spend`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer test-token'
                },
                body: JSON.stringify({
                    userId: 'test_user',
                    amount: 50,
                    reason: 'Quick test purchase'
                })
            });

            if (!spendResponse.ok) {
                throw new Error(`Token spend failed: ${spendResponse.status}`);
            }

            console.log('   ✅ Token Spend API: Working');

        } catch (error) {
            throw new Error(`API functionality test failed: ${error.message}`);
        }

        console.log('');
    }

    async testUserSimulation() {
        console.log('👤 Testing User Simulation...');

        try {
            // Reset nonce tracking before testing
            try {
                const resetResponse = await fetch(`${this.config.apiBaseUrl}/wallet/reset-nonce`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer secure-token-for-development'
                    }
                });
                if (resetResponse.ok) {
                    console.log('🔄 Nonce tracking reset for testing');
                }
            } catch (error) {
                console.log('⚠️ Could not reset nonce tracking:', error.message);
            }

            // Create a test user simulator
            const mockAccount = {
                address: '******************************************', // Hardhat account #1
                balance: '10000',
                balanceWei: '10000000000000000000000'
            };

            const grinder = new UserSimulator({
                type: 'grinder',
                account: mockAccount,
                config: this.config,
                id: 'quick_test_grinder'
            });

            // Test token award simulation with REAL game economics using proper reward system
            const completionData = {
                completed: true,
                levelNumber: 1,
                totalEnemies: 50,
                enemiesDefeated: 50 // Perfect completion
            };

            const levelConfig = {
                totalEnemies: 50
            };

            // Use REAL reward calculation
            const rewardData = grinder.tokenEconomyManager.calculateLevelReward(completionData, levelConfig);

            if (rewardData.totalReward > 0) {
                await grinder.awardTokens(rewardData.totalReward, 'Quick test level completion');
                console.log('   ✅ Token Award Simulation: Working');
            } else {
                console.log(`   ⚠️ Token Award Simulation: No reward (${rewardData.breakdown.reason})`);
            }

            // Test token spend simulation (should work for non-grinders)
            const whale = new UserSimulator({
                type: 'whale',
                account: mockAccount,
                config: this.config,
                id: 'quick_test_whale'
            });

            // Test with REAL power-up cost: SPREAD_AMMO = 7500 WISH → 750 WISH (90% off) → 7.5 ETH
            await whale.spendTokens(7.5, 'Quick test purchase');
            console.log('   ✅ Token Spend Simulation: Working');

            // Test environment creation simulation
            const creator = new UserSimulator({
                type: 'creator',
                account: mockAccount,
                config: this.config,
                id: 'quick_test_creator'
            });

            // Note: This might fail if AI services are not available, which is OK for quick test
            try {
                await creator.simulateEnvironmentCreation();
                console.log('   ✅ Environment Creation: Working');
            } catch (error) {
                console.log('   ⚠️ Environment Creation: Skipped (AI services may not be available)');
            }

        } catch (error) {
            throw new Error(`User simulation test failed: ${error.message}`);
        }

        console.log('');
    }
}

// Run quick test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const quickTest = new QuickTest();
    quickTest.run().catch(error => {
        console.error('Quick test failed:', error);
        process.exit(1);
    });
}

export { QuickTest };
